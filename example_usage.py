"""
MiniMax流式语音合成使用示例
"""

import time
import os
from streaming_tts_service import StreamingTTSService, StreamingTTSConfig
from minimax_tts import VoiceType, AudioFormat
from config import get_config, validate_config, setup_config


def example_basic_usage():
    """基础使用示例"""
    print("=== 基础流式语音合成示例 ===")

    # 验证配置
    is_valid, message = validate_config()
    if not is_valid:
        print(f"配置无效: {message}")
        print("请运行 'python config.py setup' 设置配置")
        return
    
    # 创建配置
    config = StreamingTTSConfig(
        voice_id=VoiceType.FEMALE_01.value,
        audio_format=AudioFormat.MP3.value,
        speed=1.0,
        vol=1.0
    )
    
    # 创建服务
    with StreamingTTSService(streaming_config=config) as tts_service:
        print(f"使用音频后端: {tts_service.audio_player.get_backend()}")
        
        # 要合成的文本
        text = "你好，这是MiniMax流式语音合成的测试。我们正在实时生成并播放这段语音。"
        
        # 定义回调函数
        def on_start():
            print("开始语音合成...")
        
        def on_progress(current_bytes, total_bytes):
            if total_bytes > 0:
                progress = (current_bytes / total_bytes) * 100
                print(f"播放进度: {progress:.1f}% ({current_bytes}/{total_bytes} 字节)")
            else:
                print(f"已播放: {current_bytes} 字节")
        
        def on_finish():
            print("语音播放完成！")
        
        def on_error(error):
            print(f"发生错误: {error}")
        
        # 开始合成和播放
        success = tts_service.synthesize_and_play(
            text,
            on_start=on_start,
            on_progress=on_progress,
            on_finish=on_finish,
            on_error=on_error
        )
        
        if success:
            print("语音合成已启动，等待完成...")
            
            # 等待播放完成
            while tts_service.is_busy():
                time.sleep(0.5)
                status = tts_service.get_status()
                print(f"状态: 合成中={status['is_synthesizing']}, 播放中={status['is_playing']}")
        else:
            print("启动语音合成失败")


def example_multiple_voices():
    """多种语音示例"""
    print("\n=== 多种语音测试示例 ===")

    # 验证配置
    is_valid, message = validate_config()
    if not is_valid:
        print(f"配置无效: {message}")
        return
    
    # 测试不同的语音
    voices_to_test = [
        (VoiceType.FEMALE_01.value, "女声1：甜美声音"),
        (VoiceType.FEMALE_02.value, "女声2：御姐声音"),
        (VoiceType.MALE_01.value, "男声1：清澈声音"),
        (VoiceType.MALE_02.value, "男声2：精英声音")
    ]
    
    with StreamingTTSService() as tts_service:
        for voice_id, description in voices_to_test:
            print(f"\n测试 {description}")
            
            # 更新语音配置
            tts_service.update_config(voice_id=voice_id)
            
            text = f"这是{description}的测试，您觉得这个声音怎么样？"
            
            success = tts_service.synthesize_and_play(
                text,
                on_start=lambda: print(f"开始播放: {description}"),
                on_finish=lambda: print(f"播放完成: {description}")
            )
            
            if success:
                # 等待播放完成
                while tts_service.is_busy():
                    time.sleep(0.1)
                
                # 间隔一秒
                time.sleep(1)
            else:
                print(f"播放失败: {description}")


def example_interactive_mode():
    """交互模式示例"""
    print("\n=== 交互模式示例 ===")

    # 验证配置
    is_valid, message = validate_config()
    if not is_valid:
        print(f"配置无效: {message}")
        return

    with StreamingTTSService() as tts_service:
        print("进入交互模式，输入文本进行语音合成")
        print("输入 'quit' 退出，'pause' 暂停，'resume' 恢复，'stop' 停止当前播放")
        print("输入 'status' 查看状态，'voices' 查看可用语音")
        
        while True:
            try:
                user_input = input("\n请输入文本: ").strip()
                
                if user_input.lower() == 'quit':
                    break
                elif user_input.lower() == 'pause':
                    tts_service.pause()
                    print("播放已暂停")
                elif user_input.lower() == 'resume':
                    tts_service.resume()
                    print("播放已恢复")
                elif user_input.lower() == 'stop':
                    tts_service.stop()
                    print("播放已停止")
                elif user_input.lower() == 'status':
                    status = tts_service.get_status()
                    print(f"服务状态: {status}")
                elif user_input.lower() == 'voices':
                    voices = tts_service.get_available_voices()
                    print(f"可用语音: {voices}")
                elif user_input:
                    if tts_service.is_busy():
                        print("服务忙碌中，请等待当前任务完成")
                    else:
                        success = tts_service.synthesize_and_play(
                            user_input,
                            on_start=lambda: print("开始合成..."),
                            on_finish=lambda: print("播放完成")
                        )
                        if not success:
                            print("启动失败")
                
            except KeyboardInterrupt:
                print("\n用户中断，退出...")
                break
            except Exception as e:
                print(f"发生错误: {e}")


def example_error_handling():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")

    # 使用错误的API密钥测试错误处理
    from config import MiniMaxConfig
    invalid_config = MiniMaxConfig(api_key="invalid_key", group_id="invalid_group")
    streaming_config = StreamingTTSConfig()

    with StreamingTTSService(invalid_config, streaming_config) as tts_service:
        def on_error(error):
            print(f"捕获到错误: {type(error).__name__}: {error}")
        
        success = tts_service.synthesize_and_play(
            "这是一个错误处理测试",
            on_error=on_error
        )
        
        if success:
            # 等待错误发生
            time.sleep(3)
        
        print("错误处理测试完成")


if __name__ == "__main__":
    print("MiniMax流式语音合成示例程序")
    print("请确保已安装必要的依赖: pip install requests pygame")
    print("或者: pip install requests pyaudio")
    print("或者: pip install requests simpleaudio")

    # 检查配置
    is_valid, message = validate_config()
    if not is_valid:
        print(f"\n配置检查失败: {message}")
        setup_choice = input("是否现在设置配置？(y/n): ").lower().startswith('y')
        if setup_choice:
            if setup_config():
                print("配置设置成功！")
            else:
                print("配置设置失败，程序退出")
                exit(1)
        else:
            print("请先设置配置后再运行示例")
            print("运行: python config.py setup")
            exit(1)

    try:
        # 运行基础示例
        example_basic_usage()

        # 运行多语音示例
        example_multiple_voices()

        # 运行交互模式（可选）
        run_interactive = input("\n是否运行交互模式？(y/n): ").lower().startswith('y')
        if run_interactive:
            example_interactive_mode()

        # 运行错误处理示例
        example_error_handling()

    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()

    print("\n示例程序结束")
