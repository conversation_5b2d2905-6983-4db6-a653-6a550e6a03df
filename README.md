# MiniMax 流式语音合成 Python SDK

基于 MiniMax T2A API 的流式语音合成 Python SDK，支持实时语音合成和播放。

## 功能特性

- ✅ 支持 MiniMax T2A 同步和流式语音合成
- ✅ 实时音频流播放
- ✅ 多种音频后端支持（pygame、pyaudio、simpleaudio）
- ✅ 多种语音类型和音频格式
- ✅ 错误处理和重试机制
- ✅ 异步播放和回调支持
- ✅ 播放控制（暂停、恢复、停止）

## 安装依赖

```bash
# 安装核心依赖
pip install requests

# 安装音频播放依赖（选择其中一个）
pip install pygame  # 推荐，跨平台支持好
# 或者
pip install pyaudio  # 低延迟音频播放
# 或者  
pip install simpleaudio  # 简单音频播放

# 一次性安装所有依赖
pip install -r requirements.txt
```

## 快速开始

### 1. 配置 API 密钥

有三种方式配置API密钥：

#### 方式1：使用配置文件（推荐）

编辑 `minimax_config.json` 文件：

```json
{
  "api_key": "your_api_key_here",
  "group_id": "your_group_id_here",
  "base_url": "https://api.minimax.chat"
}
```

或者使用交互式配置：

```bash
python config.py setup
```

#### 方式2：使用环境变量

```bash
export MINIMAX_API_KEY="your_api_key_here"
export MINIMAX_GROUP_ID="your_group_id_here"
```

#### 方式3：在代码中直接设置

```python
from config import MiniMaxConfig
config = MiniMaxConfig(
    api_key="your_api_key_here",
    group_id="your_group_id_here"
)
```

### 2. 快速开始

运行快速开始示例：

```bash
python quick_start.py
```

### 3. 基础使用

```python
from streaming_tts_service import StreamingTTSService, StreamingTTSConfig
from minimax_tts import VoiceType, AudioFormat

# 创建配置
config = StreamingTTSConfig(
    voice_id=VoiceType.FEMALE_01.value,
    audio_format=AudioFormat.MP3.value,
    speed=1.0,
    vol=1.0
)

# 创建服务（自动从配置文件加载API密钥）
with StreamingTTSService(streaming_config=config) as tts_service:
    # 合成并播放
    text = "你好，这是MiniMax流式语音合成的测试。"

    success = tts_service.synthesize_and_play(
        text,
        on_start=lambda: print("开始合成..."),
        on_finish=lambda: print("播放完成"),
        on_error=lambda e: print(f"错误: {e}")
    )

    if success:
        # 等待播放完成
        while tts_service.is_busy():
            time.sleep(0.1)
```

### 4. 运行完整示例

```bash
python example_usage.py
```

## API 文档

### StreamingTTSService

主要的流式语音合成服务类。

#### 初始化

```python
# 方式1：使用配置文件（推荐）
service = StreamingTTSService(streaming_config=config)

# 方式2：使用自定义配置
from config import MiniMaxConfig
minimax_config = MiniMaxConfig(api_key="...", group_id="...")
service = StreamingTTSService(minimax_config, streaming_config=config)

# 方式3：向后兼容的方式
service = StreamingTTSService.from_credentials(api_key, group_id, streaming_config=config)
```

参数：
- `minimax_config`: 可选的 MiniMaxConfig 配置对象，如果为None则从配置文件加载
- `streaming_config`: 可选的 StreamingTTSConfig 配置对象

#### 主要方法

##### synthesize_and_play()

流式合成并播放语音。

```python
success = service.synthesize_and_play(
    text,
    on_start=None,
    on_progress=None, 
    on_finish=None,
    on_error=None
)
```

参数：
- `text`: 要合成的文本
- `on_start`: 开始回调函数
- `on_progress`: 进度回调函数 `(current_bytes, total_bytes)`
- `on_finish`: 完成回调函数
- `on_error`: 错误回调函数 `(exception)`

返回：
- `bool`: 是否成功启动

##### 播放控制

```python
service.stop()     # 停止播放
service.pause()    # 暂停播放
service.resume()   # 恢复播放
```

##### 状态查询

```python
service.is_busy()           # 是否忙碌
service.get_status()        # 获取详细状态
service.get_available_voices()  # 获取可用语音列表
```

### StreamingTTSConfig

流式TTS配置类。

```python
config = StreamingTTSConfig(
    # TTS配置
    voice_id=VoiceType.FEMALE_01.value,
    audio_format=AudioFormat.MP3.value,
    speed=1.0,
    vol=1.0,
    pitch=0.0,
    
    # 音频播放配置
    sample_rate=44100,
    channels=2,
    buffer_size=4096,
    
    # 流式配置
    chunk_size=8192,
    max_retries=3,
    retry_delay=1.0
)
```

### 支持的语音类型

```python
from minimax_tts import VoiceType

VoiceType.MALE_01     # 男声1：清澈
VoiceType.MALE_02     # 男声2：精英
VoiceType.MALE_03     # 男声3：霸道
VoiceType.MALE_04     # 男声4：大学生
VoiceType.FEMALE_01   # 女声1：少女
VoiceType.FEMALE_02   # 女声2：御姐
VoiceType.FEMALE_03   # 女声3：成熟
VoiceType.FEMALE_04   # 女声4：甜美
```

### 支持的音频格式

```python
from minimax_tts import AudioFormat

AudioFormat.MP3   # MP3格式
AudioFormat.WAV   # WAV格式
AudioFormat.PCM   # PCM格式
```

## 配置管理

### 配置文件管理

```bash
# 交互式设置配置
python config.py setup

# 创建默认配置文件
python config.py create

# 验证当前配置
python config.py validate

# 显示当前配置
python config.py show
```

### 配置优先级

配置加载优先级（从高到低）：
1. 环境变量 (`MINIMAX_API_KEY`, `MINIMAX_GROUP_ID`, `MINIMAX_BASE_URL`)
2. 配置文件 (`minimax_config.json`)
3. 代码中直接设置的值

### 配置类使用

```python
from config import get_config, validate_config, MiniMaxConfig

# 获取当前配置
config = get_config()

# 验证配置
is_valid, message = validate_config()

# 创建自定义配置
custom_config = MiniMaxConfig(
    api_key="your_key",
    group_id="your_group",
    base_url="https://api.minimax.chat"
)
```

## 错误处理

SDK 提供了完善的错误处理机制：

```python
def on_error(error):
    if isinstance(error, MiniMaxTTSError):
        print(f"TTS错误: {error}")
    elif isinstance(error, AudioPlayerError):
        print(f"播放错误: {error}")
    else:
        print(f"其他错误: {error}")

service.synthesize_and_play(text, on_error=on_error)
```

## 注意事项

1. **API 密钥安全**: 请妥善保管您的 MiniMax API 密钥，不要在代码中硬编码。

2. **音频后端**: SDK 会自动选择可用的音频后端，推荐安装 pygame。

3. **网络连接**: 流式合成需要稳定的网络连接，SDK 内置了重试机制。

4. **并发限制**: 同一时间只能运行一个合成任务，请等待当前任务完成。

5. **资源清理**: 使用 `with` 语句或手动调用 `close()` 方法清理资源。

## 许可证

MIT License

## 支持

如有问题请提交 Issue 或联系技术支持。
