"""
流式音频播放器
支持实时播放音频流数据
"""

import io
import time
import threading
import queue
from typing import Iterator, Optional, Callable
from dataclasses import dataclass
import tempfile
import os

try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False

try:
    import pyaudio
    import wave
    PYAUDIO_AVAILABLE = True
except ImportError:
    PYAUDIO_AVAILABLE = False

try:
    import simpleaudio as sa
    SIMPLEAUDIO_AVAILABLE = True
except ImportError:
    SIMPLEAUDIO_AVAILABLE = False


@dataclass
class AudioConfig:
    """音频配置"""
    sample_rate: int = 44100
    channels: int = 2
    sample_width: int = 2  # 2 bytes = 16 bit
    buffer_size: int = 4096


class AudioPlayerError(Exception):
    """音频播放器异常"""
    pass


class StreamingAudioPlayer:
    """流式音频播放器"""
    
    def __init__(self, config: Optional[AudioConfig] = None):
        """
        初始化播放器
        
        Args:
            config: 音频配置
        """
        self.config = config or AudioConfig()
        self.is_playing = False
        self.is_paused = False
        self.audio_queue = queue.Queue()
        self.play_thread = None
        self._stop_event = threading.Event()
        
        # 选择可用的音频后端
        self.backend = self._select_backend()
        if not self.backend:
            raise AudioPlayerError("没有找到可用的音频播放后端，请安装 pygame、pyaudio 或 simpleaudio")
    
    def _select_backend(self) -> Optional[str]:
        """选择可用的音频后端"""
        if PYGAME_AVAILABLE:
            return "pygame"
        elif PYAUDIO_AVAILABLE:
            return "pyaudio"
        elif SIMPLEAUDIO_AVAILABLE:
            return "simpleaudio"
        return None
    
    def _init_pygame(self):
        """初始化pygame音频"""
        if not PYGAME_AVAILABLE:
            raise AudioPlayerError("pygame不可用")
        
        import pygame
        pygame.mixer.pre_init(
            frequency=self.config.sample_rate,
            size=-16,  # 16-bit signed
            channels=self.config.channels,
            buffer=self.config.buffer_size
        )
        pygame.mixer.init()
    
    def _init_pyaudio(self):
        """初始化pyaudio"""
        if not PYAUDIO_AVAILABLE:
            raise AudioPlayerError("pyaudio不可用")
        
        self.pyaudio_instance = pyaudio.PyAudio()
        self.pyaudio_stream = self.pyaudio_instance.open(
            format=pyaudio.paInt16,
            channels=self.config.channels,
            rate=self.config.sample_rate,
            output=True,
            frames_per_buffer=self.config.buffer_size
        )
    
    def play_stream(self, audio_stream: Iterator[bytes], 
                   on_start: Optional[Callable] = None,
                   on_finish: Optional[Callable] = None,
                   on_error: Optional[Callable[[Exception], None]] = None):
        """
        播放音频流
        
        Args:
            audio_stream: 音频数据流迭代器
            on_start: 开始播放回调
            on_finish: 播放完成回调
            on_error: 错误回调
        """
        if self.is_playing:
            raise AudioPlayerError("播放器正在播放中")
        
        self.is_playing = True
        self.is_paused = False
        self._stop_event.clear()
        
        def play_worker():
            try:
                if on_start:
                    on_start()
                
                if self.backend == "pygame":
                    self._play_with_pygame(audio_stream)
                elif self.backend == "pyaudio":
                    self._play_with_pyaudio(audio_stream)
                elif self.backend == "simpleaudio":
                    self._play_with_simpleaudio(audio_stream)
                
                if on_finish:
                    on_finish()
                    
            except Exception as e:
                if on_error:
                    on_error(e)
                else:
                    print(f"播放错误: {e}")
            finally:
                self.is_playing = False
        
        self.play_thread = threading.Thread(target=play_worker, daemon=True)
        self.play_thread.start()
    
    def _play_with_pygame(self, audio_stream: Iterator[bytes]):
        """使用pygame播放音频流"""
        self._init_pygame()
        import pygame
        
        # 创建临时文件来存储音频数据
        temp_files = []
        
        try:
            for chunk in audio_stream:
                if self._stop_event.is_set():
                    break
                
                # 创建临时文件
                with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_file:
                    temp_file.write(chunk)
                    temp_file_path = temp_file.name
                    temp_files.append(temp_file_path)
                
                # 播放音频块
                try:
                    pygame.mixer.music.load(temp_file_path)
                    pygame.mixer.music.play()
                    
                    # 等待播放完成
                    while pygame.mixer.music.get_busy():
                        if self._stop_event.is_set():
                            pygame.mixer.music.stop()
                            break
                        time.sleep(0.1)
                        
                except Exception as e:
                    print(f"播放音频块失败: {e}")
                    
        finally:
            # 清理临时文件
            for temp_file_path in temp_files:
                try:
                    os.unlink(temp_file_path)
                except:
                    pass
    
    def _play_with_pyaudio(self, audio_stream: Iterator[bytes]):
        """使用pyaudio播放音频流"""
        self._init_pyaudio()
        
        try:
            for chunk in audio_stream:
                if self._stop_event.is_set():
                    break
                
                # 对于MP3数据，需要先解码
                # 这里简化处理，假设是PCM数据
                self.pyaudio_stream.write(chunk)
                
        finally:
            self.pyaudio_stream.stop_stream()
            self.pyaudio_stream.close()
            self.pyaudio_instance.terminate()
    
    def _play_with_simpleaudio(self, audio_stream: Iterator[bytes]):
        """使用simpleaudio播放音频流"""
        import simpleaudio as sa
        
        # 收集所有音频数据
        audio_data = b''
        for chunk in audio_stream:
            if self._stop_event.is_set():
                break
            audio_data += chunk
        
        if audio_data:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_file_path = temp_file.name
            
            try:
                wave_obj = sa.WaveObject.from_wave_file(temp_file_path)
                play_obj = wave_obj.play()
                
                # 等待播放完成
                while play_obj.is_playing():
                    if self._stop_event.is_set():
                        play_obj.stop()
                        break
                    time.sleep(0.1)
                    
            finally:
                os.unlink(temp_file_path)
    
    def stop(self):
        """停止播放"""
        self._stop_event.set()
        self.is_playing = False
        
        if self.play_thread and self.play_thread.is_alive():
            self.play_thread.join(timeout=2.0)
    
    def pause(self):
        """暂停播放"""
        self.is_paused = True
        if self.backend == "pygame" and PYGAME_AVAILABLE:
            import pygame
            pygame.mixer.music.pause()
    
    def resume(self):
        """恢复播放"""
        self.is_paused = False
        if self.backend == "pygame" and PYGAME_AVAILABLE:
            import pygame
            pygame.mixer.music.unpause()
    
    def is_playing_audio(self) -> bool:
        """检查是否正在播放"""
        return self.is_playing and not self.is_paused
    
    def get_backend(self) -> str:
        """获取当前使用的音频后端"""
        return self.backend
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.stop()
