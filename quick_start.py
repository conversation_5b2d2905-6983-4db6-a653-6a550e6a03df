"""
MiniMax TTS 快速开始示例
"""

import time
from streaming_tts_service import StreamingTTSService, StreamingTTSConfig
from minimax_tts import VoiceType, AudioFormat
from config import validate_config, setup_config


def main():
    """主函数"""
    print("🎵 MiniMax 流式语音合成快速开始")
    print("=" * 50)
    
    # 检查配置
    is_valid, message = validate_config()
    if not is_valid:
        print(f"❌ 配置检查失败: {message}")
        print("\n请选择操作:")
        print("1. 交互式设置配置")
        print("2. 退出程序")
        
        choice = input("请输入选择 (1/2): ").strip()
        if choice == "1":
            if setup_config():
                print("✅ 配置设置成功！")
            else:
                print("❌ 配置设置失败")
                return
        else:
            print("程序退出")
            return
    else:
        print("✅ 配置验证通过")
    
    # 创建配置
    config = StreamingTTSConfig(
        voice_id=VoiceType.FEMALE_01.value,  # 使用女声1
        audio_format=AudioFormat.MP3.value,
        speed=1.0,
        vol=1.0
    )
    
    # 创建服务
    print("\n🚀 初始化语音合成服务...")
    try:
        with StreamingTTSService(streaming_config=config) as tts_service:
            print(f"✅ 服务初始化成功，使用音频后端: {tts_service.audio_player.get_backend()}")
            
            # 要合成的文本
            text = "你好！欢迎使用MiniMax流式语音合成服务。这是一个快速开始的示例，展示了如何实时生成和播放语音。"
            
            print(f"\n📝 准备合成文本: {text}")
            print("\n🎤 开始语音合成和播放...")
            
            # 定义回调函数
            def on_start():
                print("▶️  开始语音合成...")
            
            def on_progress(current_bytes, total_bytes):
                print(f"📊 播放进度: {current_bytes} 字节")
            
            def on_finish():
                print("✅ 语音播放完成！")
            
            def on_error(error):
                print(f"❌ 发生错误: {error}")
            
            # 开始合成和播放
            success = tts_service.synthesize_and_play(
                text,
                on_start=on_start,
                on_progress=on_progress,
                on_finish=on_finish,
                on_error=on_error
            )
            
            if success:
                print("🎵 语音合成已启动，请等待播放完成...")
                
                # 等待播放完成
                while tts_service.is_busy():
                    time.sleep(0.5)
                    # 显示状态
                    status = tts_service.get_status()
                    if status['is_synthesizing']:
                        print("🔄 正在合成中...")
                    elif status['is_playing']:
                        print("🔊 正在播放中...")
                
                print("\n🎉 快速开始示例完成！")
                
                # 询问是否继续其他示例
                continue_choice = input("\n是否运行完整示例程序？(y/n): ").lower().startswith('y')
                if continue_choice:
                    print("\n启动完整示例程序...")
                    import example_usage
                    example_usage.example_basic_usage()
                    
            else:
                print("❌ 启动语音合成失败")
                
    except Exception as e:
        print(f"❌ 服务初始化失败: {e}")
        print("请检查网络连接和API配置")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n感谢使用 MiniMax TTS！")
