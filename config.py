"""
MiniMax TTS 配置文件
"""

import os
import json
from typing import Optional, Dict, Any
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class MiniMaxConfig:
    """MiniMax API配置"""
    api_key: str = ""
    group_id: str = ""
    base_url: str = "https://api.minimax.chat"
    
    def is_valid(self) -> bool:
        """检查配置是否有效"""
        return bool(self.api_key and self.group_id)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MiniMaxConfig':
        """从字典创建配置"""
        return cls(**data)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "minimax_config.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = Path(config_file)
        self._config: Optional[MiniMaxConfig] = None
    
    def load_config(self) -> MiniMaxConfig:
        """
        加载配置
        优先级：环境变量 > 配置文件 > 默认值
        
        Returns:
            MiniMax配置对象
        """
        config = MiniMaxConfig()
        
        # 1. 尝试从配置文件加载
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                config = MiniMaxConfig.from_dict(file_config)
                print(f"从配置文件加载配置: {self.config_file}")
            except Exception as e:
                print(f"加载配置文件失败: {e}")
        
        # 2. 环境变量覆盖配置文件
        env_api_key = os.getenv("MINIMAX_API_KEY")
        env_group_id = os.getenv("MINIMAX_GROUP_ID")
        env_base_url = os.getenv("MINIMAX_BASE_URL")
        
        if env_api_key:
            config.api_key = env_api_key
            print("使用环境变量 MINIMAX_API_KEY")
        
        if env_group_id:
            config.group_id = env_group_id
            print("使用环境变量 MINIMAX_GROUP_ID")
        
        if env_base_url:
            config.base_url = env_base_url
            print("使用环境变量 MINIMAX_BASE_URL")
        
        self._config = config
        return config
    
    def save_config(self, config: MiniMaxConfig) -> bool:
        """
        保存配置到文件
        
        Args:
            config: 要保存的配置
            
        Returns:
            是否保存成功
        """
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config.to_dict(), f, indent=2, ensure_ascii=False)
            print(f"配置已保存到: {self.config_file}")
            self._config = config
            return True
        except Exception as e:
            print(f"保存配置失败: {e}")
            return False
    
    def create_default_config(self) -> bool:
        """
        创建默认配置文件
        
        Returns:
            是否创建成功
        """
        default_config = MiniMaxConfig(
            api_key="your_api_key_here",
            group_id="your_group_id_here",
            base_url="https://api.minimax.chat"
        )
        
        return self.save_config(default_config)
    
    def get_config(self) -> MiniMaxConfig:
        """
        获取当前配置
        
        Returns:
            当前配置对象
        """
        if self._config is None:
            self._config = self.load_config()
        return self._config
    
    def update_config(self, **kwargs) -> bool:
        """
        更新配置
        
        Args:
            **kwargs: 要更新的配置项
            
        Returns:
            是否更新成功
        """
        config = self.get_config()
        
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)
            else:
                print(f"警告: 未知的配置项 {key}")
        
        return self.save_config(config)
    
    def validate_config(self) -> tuple[bool, str]:
        """
        验证配置
        
        Returns:
            (是否有效, 错误信息)
        """
        config = self.get_config()
        
        if not config.api_key or config.api_key == "your_api_key_here":
            return False, "API密钥未设置或使用默认值"
        
        if not config.group_id or config.group_id == "your_group_id_here":
            return False, "组织ID未设置或使用默认值"
        
        if not config.base_url:
            return False, "基础URL未设置"
        
        return True, "配置有效"
    
    def interactive_setup(self) -> bool:
        """
        交互式配置设置
        
        Returns:
            是否设置成功
        """
        print("=== MiniMax TTS 配置设置 ===")
        print("请输入您的MiniMax API配置信息")
        print("(直接回车保持当前值)")
        
        current_config = self.get_config()
        
        # API密钥
        current_key = current_config.api_key if current_config.api_key != "your_api_key_here" else ""
        api_key = input(f"API密钥 [{current_key[:10]}...]: ").strip()
        if not api_key:
            api_key = current_config.api_key
        
        # 组织ID
        current_group = current_config.group_id if current_config.group_id != "your_group_id_here" else ""
        group_id = input(f"组织ID [{current_group}]: ").strip()
        if not group_id:
            group_id = current_config.group_id
        
        # 基础URL
        base_url = input(f"基础URL [{current_config.base_url}]: ").strip()
        if not base_url:
            base_url = current_config.base_url
        
        # 创建新配置
        new_config = MiniMaxConfig(
            api_key=api_key,
            group_id=group_id,
            base_url=base_url
        )
        
        # 验证配置
        is_valid, message = self.validate_config_obj(new_config)
        if not is_valid:
            print(f"配置验证失败: {message}")
            return False
        
        # 保存配置
        success = self.save_config(new_config)
        if success:
            print("配置设置成功！")
        
        return success
    
    def validate_config_obj(self, config: MiniMaxConfig) -> tuple[bool, str]:
        """
        验证配置对象
        
        Args:
            config: 要验证的配置对象
            
        Returns:
            (是否有效, 错误信息)
        """
        if not config.api_key or config.api_key == "your_api_key_here":
            return False, "API密钥未设置或使用默认值"
        
        if not config.group_id or config.group_id == "your_group_id_here":
            return False, "组织ID未设置或使用默认值"
        
        if not config.base_url:
            return False, "基础URL未设置"
        
        return True, "配置有效"


# 全局配置管理器实例
config_manager = ConfigManager()


def get_config() -> MiniMaxConfig:
    """获取全局配置"""
    return config_manager.get_config()


def setup_config() -> bool:
    """设置配置"""
    return config_manager.interactive_setup()


def validate_config() -> tuple[bool, str]:
    """验证配置"""
    return config_manager.validate_config()


if __name__ == "__main__":
    # 命令行配置工具
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "setup":
            # 交互式设置
            config_manager.interactive_setup()
        elif command == "create":
            # 创建默认配置文件
            config_manager.create_default_config()
            print("默认配置文件已创建，请编辑 minimax_config.json 文件")
        elif command == "validate":
            # 验证配置
            is_valid, message = config_manager.validate_config()
            print(f"配置验证: {'通过' if is_valid else '失败'}")
            print(f"信息: {message}")
        elif command == "show":
            # 显示当前配置
            config = config_manager.get_config()
            print("当前配置:")
            print(f"  API密钥: {config.api_key[:10]}..." if config.api_key else "  API密钥: 未设置")
            print(f"  组织ID: {config.group_id}")
            print(f"  基础URL: {config.base_url}")
        else:
            print("未知命令")
    else:
        print("MiniMax TTS 配置管理工具")
        print("用法:")
        print("  python config.py setup     - 交互式设置配置")
        print("  python config.py create    - 创建默认配置文件")
        print("  python config.py validate  - 验证当前配置")
        print("  python config.py show      - 显示当前配置")
