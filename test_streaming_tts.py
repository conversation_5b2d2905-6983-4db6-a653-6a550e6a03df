"""
MiniMax流式语音合成测试
"""

import pytest
import time
import os
from unittest.mock import Mock, patch, MagicMock
from streaming_tts_service import StreamingTTSService, StreamingTTSConfig
from minimax_tts import MiniMaxTTSClient, VoiceType, AudioFormat, MiniMaxTTSError
from audio_player import StreamingAudioPlayer, AudioPlayerError
from config import MiniMaxConfig, ConfigManager


class TestMiniMaxTTSClient:
    """测试MiniMax TTS客户端"""
    
    def test_client_initialization(self):
        """测试客户端初始化"""
        config = MiniMaxConfig(api_key="test_key", group_id="test_group")
        client = MiniMaxTTSClient(config)
        assert client.api_key == "test_key"
        assert client.group_id == "test_group"
        assert client.base_url == "https://api.minimax.chat"
    
    @patch('requests.Session.post')
    def test_sync_synthesis_success(self, mock_post):
        """测试同步合成成功"""
        # 模拟成功响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.content = b"fake_audio_data"
        mock_response.headers = {'content-type': 'audio/mpeg'}
        mock_post.return_value = mock_response
        
        config = MiniMaxConfig(api_key="test_key", group_id="test_group")
        client = MiniMaxTTSClient(config)
        result = client.synthesize_sync("测试文本")
        
        assert result == b"fake_audio_data"
        mock_post.assert_called_once()
    
    @patch('requests.Session.post')
    def test_sync_synthesis_error(self, mock_post):
        """测试同步合成错误"""
        # 模拟错误响应
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.json.return_value = {"error": {"message": "Invalid request"}}
        mock_post.return_value = mock_response
        
        config = MiniMaxConfig(api_key="test_key", group_id="test_group")
        client = MiniMaxTTSClient(config)
        
        with pytest.raises(MiniMaxTTSError):
            client.synthesize_sync("测试文本")
    
    @patch('requests.Session.post')
    def test_stream_synthesis(self, mock_post):
        """测试流式合成"""
        # 模拟流式响应
        mock_response = Mock()
        mock_response.iter_content.return_value = [b"chunk1", b"chunk2", b"chunk3"]
        mock_post.return_value = mock_response
        
        config = MiniMaxConfig(api_key="test_key", group_id="test_group")
        client = MiniMaxTTSClient(config)
        chunks = list(client.synthesize_stream("测试文本"))
        
        assert chunks == [b"chunk1", b"chunk2", b"chunk3"]
        mock_post.assert_called_once()


class TestStreamingAudioPlayer:
    """测试流式音频播放器"""
    
    def test_player_initialization(self):
        """测试播放器初始化"""
        # 模拟没有可用的音频后端
        with patch('audio_player.PYGAME_AVAILABLE', False), \
             patch('audio_player.PYAUDIO_AVAILABLE', False), \
             patch('audio_player.SIMPLEAUDIO_AVAILABLE', False):
            
            with pytest.raises(AudioPlayerError):
                StreamingAudioPlayer()
    
    @patch('audio_player.PYGAME_AVAILABLE', True)
    def test_backend_selection(self):
        """测试音频后端选择"""
        player = StreamingAudioPlayer()
        assert player.backend == "pygame"
    
    def test_player_state(self):
        """测试播放器状态"""
        with patch('audio_player.PYGAME_AVAILABLE', True):
            player = StreamingAudioPlayer()
            
            assert not player.is_playing
            assert not player.is_paused
            assert player.get_backend() == "pygame"


class TestStreamingTTSService:
    """测试流式TTS服务"""
    
    @patch('streaming_tts_service.MiniMaxTTSClient')
    @patch('streaming_tts_service.StreamingAudioPlayer')
    def test_service_initialization(self, mock_player_class, mock_client_class):
        """测试服务初始化"""
        mock_client = Mock()
        mock_player = Mock()
        mock_client_class.return_value = mock_client
        mock_player_class.return_value = mock_player
        
        minimax_config = MiniMaxConfig(api_key="test_key", group_id="test_group")
        streaming_config = StreamingTTSConfig()
        service = StreamingTTSService(minimax_config, streaming_config)

        assert service.config == streaming_config
        assert not service.is_synthesizing
        assert not service.is_playing
        mock_client_class.assert_called_once_with(minimax_config)
    
    @patch('streaming_tts_service.MiniMaxTTSClient')
    @patch('streaming_tts_service.StreamingAudioPlayer')
    def test_synthesize_and_play_busy(self, mock_player_class, mock_client_class):
        """测试服务忙碌时的行为"""
        minimax_config = MiniMaxConfig(api_key="test_key", group_id="test_group")
        service = StreamingTTSService(minimax_config)
        service.is_synthesizing = True
        
        error_callback = Mock()
        result = service.synthesize_and_play("测试", on_error=error_callback)
        
        assert not result
        error_callback.assert_called_once()
    
    @patch('streaming_tts_service.MiniMaxTTSClient')
    @patch('streaming_tts_service.StreamingAudioPlayer')
    def test_service_status(self, mock_player_class, mock_client_class):
        """测试服务状态"""
        mock_player = Mock()
        mock_player.get_backend.return_value = "pygame"
        mock_player_class.return_value = mock_player
        
        minimax_config = MiniMaxConfig(api_key="test_key", group_id="test_group")
        streaming_config = StreamingTTSConfig(voice_id="test_voice")
        service = StreamingTTSService(minimax_config, streaming_config)
        
        status = service.get_status()
        
        assert "is_synthesizing" in status
        assert "is_playing" in status
        assert "audio_backend" in status
        assert "config" in status
        assert status["config"]["voice_id"] == "test_voice"
    
    @patch('streaming_tts_service.MiniMaxTTSClient')
    @patch('streaming_tts_service.StreamingAudioPlayer')
    def test_config_update(self, mock_player_class, mock_client_class):
        """测试配置更新"""
        minimax_config = MiniMaxConfig(api_key="test_key", group_id="test_group")
        service = StreamingTTSService(minimax_config)
        
        service.update_config(voice_id="new_voice", speed=1.5)
        
        assert service.config.voice_id == "new_voice"
        assert service.config.speed == 1.5


class TestIntegration:
    """集成测试"""
    
    def test_voice_types_enum(self):
        """测试语音类型枚举"""
        assert VoiceType.FEMALE_01.value == "female-shaonv"
        assert VoiceType.MALE_01.value == "male-qn-qingse"
    
    def test_audio_format_enum(self):
        """测试音频格式枚举"""
        assert AudioFormat.MP3.value == "mp3"
        assert AudioFormat.WAV.value == "wav"
        assert AudioFormat.PCM.value == "pcm"
    
    def test_config_defaults(self):
        """测试配置默认值"""
        config = StreamingTTSConfig()
        
        assert config.voice_id == VoiceType.FEMALE_01.value
        assert config.audio_format == AudioFormat.MP3.value
        assert config.speed == 1.0
        assert config.vol == 1.0
        assert config.pitch == 0.0


def test_example_with_mock():
    """使用模拟数据测试完整流程"""
    
    # 模拟环境变量
    with patch.dict(os.environ, {
        'MINIMAX_API_KEY': 'test_key',
        'MINIMAX_GROUP_ID': 'test_group'
    }):
        
        # 模拟TTS客户端
        with patch('streaming_tts_service.MiniMaxTTSClient') as mock_client_class:
            mock_client = Mock()
            mock_client.synthesize_stream.return_value = iter([b"chunk1", b"chunk2"])
            mock_client_class.return_value = mock_client
            
            # 模拟音频播放器
            with patch('streaming_tts_service.StreamingAudioPlayer') as mock_player_class:
                mock_player = Mock()
                mock_player.get_backend.return_value = "pygame"
                mock_player_class.return_value = mock_player
                
                # 创建服务并测试
                minimax_config = MiniMaxConfig(api_key="test_key", group_id="test_group")
                service = StreamingTTSService(minimax_config)
                
                # 测试回调
                start_called = False
                finish_called = False
                
                def on_start():
                    nonlocal start_called
                    start_called = True
                
                def on_finish():
                    nonlocal finish_called
                    finish_called = True
                
                # 模拟播放流程
                def mock_play_stream(stream, on_start=None, on_finish=None, on_error=None):
                    if on_start:
                        on_start()
                    # 消费流数据
                    list(stream)
                    if on_finish:
                        on_finish()
                
                mock_player.play_stream.side_effect = mock_play_stream
                
                # 执行合成和播放
                success = service.synthesize_and_play(
                    "测试文本",
                    on_start=on_start,
                    on_finish=on_finish
                )
                
                assert success
                
                # 等待线程完成
                if service._current_synthesis_thread:
                    service._current_synthesis_thread.join(timeout=1.0)
                
                # 验证调用
                mock_client.synthesize_stream.assert_called_once()
                mock_player.play_stream.assert_called_once()


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
