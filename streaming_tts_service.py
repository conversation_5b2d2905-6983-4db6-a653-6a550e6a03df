"""
流式语音合成服务
整合MiniMax TTS API和音频播放功能
"""

import time
import threading
from typing import Optional, Callable, Iterator
from dataclasses import dataclass

from minimax_tts import MiniMaxTTSClient, TTSConfig, VoiceType, AudioFormat, MiniMaxTTSError
from audio_player import StreamingAudioPlayer, AudioConfig, AudioPlayerError


@dataclass
class StreamingTTSConfig:
    """流式TTS配置"""
    # TTS配置
    voice_id: str = VoiceType.FEMALE_01.value
    audio_format: str = AudioFormat.MP3.value
    speed: float = 1.0
    vol: float = 1.0
    pitch: float = 0.0
    
    # 音频播放配置
    sample_rate: int = 44100
    channels: int = 2
    buffer_size: int = 4096
    
    # 流式配置
    chunk_size: int = 8192
    max_retries: int = 3
    retry_delay: float = 1.0


class StreamingTTSService:
    """流式语音合成服务"""
    
    def __init__(self, api_key: str, group_id: str, config: Optional[StreamingTTSConfig] = None):
        """
        初始化服务
        
        Args:
            api_key: MiniMax API密钥
            group_id: 组织ID
            config: 流式TTS配置
        """
        self.config = config or StreamingTTSConfig()
        
        # 初始化TTS客户端
        self.tts_client = MiniMaxTTSClient(api_key, group_id)
        
        # 初始化音频播放器
        audio_config = AudioConfig(
            sample_rate=self.config.sample_rate,
            channels=self.config.channels,
            buffer_size=self.config.buffer_size
        )
        self.audio_player = StreamingAudioPlayer(audio_config)
        
        # 状态管理
        self.is_synthesizing = False
        self.is_playing = False
        self._current_synthesis_thread = None
    
    def synthesize_and_play(self, 
                          text: str,
                          on_start: Optional[Callable] = None,
                          on_progress: Optional[Callable[[int, int], None]] = None,
                          on_finish: Optional[Callable] = None,
                          on_error: Optional[Callable[[Exception], None]] = None) -> bool:
        """
        流式合成并播放语音
        
        Args:
            text: 要合成的文本
            on_start: 开始回调
            on_progress: 进度回调 (当前字节数, 总字节数)
            on_finish: 完成回调
            on_error: 错误回调
            
        Returns:
            是否成功启动
        """
        if self.is_synthesizing or self.is_playing:
            if on_error:
                on_error(Exception("服务正在运行中"))
            return False
        
        def synthesis_worker():
            try:
                self.is_synthesizing = True
                
                if on_start:
                    on_start()
                
                # 创建TTS配置
                tts_config = TTSConfig(
                    voice_id=self.config.voice_id,
                    audio_format=self.config.audio_format,
                    speed=self.config.speed,
                    vol=self.config.vol,
                    pitch=self.config.pitch
                )
                
                # 开始流式合成
                audio_stream = self._synthesize_with_retry(text, tts_config)
                
                # 开始播放
                self._play_audio_stream(audio_stream, on_progress, on_finish, on_error)
                
            except Exception as e:
                if on_error:
                    on_error(e)
                else:
                    print(f"合成播放错误: {e}")
            finally:
                self.is_synthesizing = False
        
        self._current_synthesis_thread = threading.Thread(target=synthesis_worker, daemon=True)
        self._current_synthesis_thread.start()
        return True
    
    def _synthesize_with_retry(self, text: str, tts_config: TTSConfig) -> Iterator[bytes]:
        """
        带重试的语音合成
        
        Args:
            text: 文本
            tts_config: TTS配置
            
        Returns:
            音频数据流
            
        Raises:
            MiniMaxTTSError: 合成失败
        """
        last_error = None
        
        for attempt in range(self.config.max_retries):
            try:
                return self.tts_client.synthesize_stream(text, tts_config)
            except MiniMaxTTSError as e:
                last_error = e
                if attempt < self.config.max_retries - 1:
                    print(f"合成失败，{self.config.retry_delay}秒后重试 (尝试 {attempt + 1}/{self.config.max_retries}): {e}")
                    time.sleep(self.config.retry_delay)
                else:
                    print(f"合成最终失败: {e}")
        
        raise last_error or MiniMaxTTSError("合成失败")
    
    def _play_audio_stream(self, 
                          audio_stream: Iterator[bytes],
                          on_progress: Optional[Callable[[int, int], None]] = None,
                          on_finish: Optional[Callable] = None,
                          on_error: Optional[Callable[[Exception], None]] = None):
        """
        播放音频流
        
        Args:
            audio_stream: 音频数据流
            on_progress: 进度回调
            on_finish: 完成回调
            on_error: 错误回调
        """
        def progress_wrapper(stream):
            """包装流以提供进度回调"""
            total_bytes = 0
            chunk_count = 0
            
            for chunk in stream:
                total_bytes += len(chunk)
                chunk_count += 1
                
                if on_progress:
                    # 估算进度（基于块数）
                    on_progress(total_bytes, -1)  # -1表示总大小未知
                
                yield chunk
        
        def play_start_callback():
            self.is_playing = True
            print("开始播放音频...")
        
        def play_finish_callback():
            self.is_playing = False
            print("音频播放完成")
            if on_finish:
                on_finish()
        
        def play_error_callback(error):
            self.is_playing = False
            print(f"播放错误: {error}")
            if on_error:
                on_error(error)
        
        # 开始播放
        wrapped_stream = progress_wrapper(audio_stream)
        self.audio_player.play_stream(
            wrapped_stream,
            on_start=play_start_callback,
            on_finish=play_finish_callback,
            on_error=play_error_callback
        )
    
    def stop(self):
        """停止合成和播放"""
        self.audio_player.stop()
        self.is_synthesizing = False
        self.is_playing = False
        
        if self._current_synthesis_thread and self._current_synthesis_thread.is_alive():
            self._current_synthesis_thread.join(timeout=2.0)
    
    def pause(self):
        """暂停播放"""
        self.audio_player.pause()
    
    def resume(self):
        """恢复播放"""
        self.audio_player.resume()
    
    def is_busy(self) -> bool:
        """检查服务是否忙碌"""
        return self.is_synthesizing or self.is_playing
    
    def get_status(self) -> dict:
        """获取服务状态"""
        return {
            "is_synthesizing": self.is_synthesizing,
            "is_playing": self.is_playing,
            "audio_backend": self.audio_player.get_backend(),
            "config": {
                "voice_id": self.config.voice_id,
                "audio_format": self.config.audio_format,
                "speed": self.config.speed,
                "vol": self.config.vol,
                "pitch": self.config.pitch
            }
        }
    
    def update_config(self, **kwargs):
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
    
    def get_available_voices(self) -> dict:
        """获取可用语音列表"""
        try:
            return self.tts_client.get_voice_list()
        except Exception as e:
            print(f"获取语音列表失败: {e}")
            return {}
    
    def close(self):
        """关闭服务"""
        self.stop()
        self.tts_client.close()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
