"""
MiniMax Text-to-Speech (T2A) API Client
支持同步和流式语音合成功能
"""

import json
import time
import requests
import threading
from typing import Iterator, Optional, Dict, Any, Callable
from dataclasses import dataclass
from enum import Enum
from config import MiniMaxConfig, get_config


class VoiceType(Enum):
    """语音类型枚举"""
    MALE_01 = "male-qn-qingse"
    MALE_02 = "male-qn-jingying" 
    MALE_03 = "male-qn-badao"
    MALE_04 = "male-qn-daxuesheng"
    FEMALE_01 = "female-shaonv"
    FEMALE_02 = "female-yujie"
    FEMALE_03 = "female-chengshu"
    FEMALE_04 = "female-tianmei"


class AudioFormat(Enum):
    """音频格式枚举"""
    MP3 = "mp3"
    WAV = "wav"
    PCM = "pcm"


@dataclass
class TTSConfig:
    """TTS配置类"""
    voice_id: str = VoiceType.FEMALE_01.value
    audio_format: str = AudioFormat.MP3.value
    bitrate: int = 128000
    speed: float = 1.0
    vol: float = 1.0
    pitch: float = 0.0


class MiniMaxTTSError(Exception):
    """MiniMax TTS异常类"""
    pass


class MiniMaxTTSClient:
    """MiniMax Text-to-Speech API客户端"""

    def __init__(self, config: Optional[MiniMaxConfig] = None):
        """
        初始化客户端

        Args:
            config: MiniMax配置对象，如果为None则从配置文件加载
        """
        if config is None:
            config = get_config()

        if not config.is_valid():
            raise MiniMaxTTSError("配置无效：请设置有效的API密钥和组织ID")

        self.config = config
        self.api_key = config.api_key
        self.group_id = config.group_id
        self.base_url = config.base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        })

    @classmethod
    def from_credentials(cls, api_key: str, group_id: str, base_url: str = "https://api.minimax.chat"):
        """
        从凭据创建客户端（向后兼容）

        Args:
            api_key: MiniMax API密钥
            group_id: 组织ID
            base_url: API基础URL
        """
        config = MiniMaxConfig(api_key=api_key, group_id=group_id, base_url=base_url)
        return cls(config)
    
    def _make_request(self, endpoint: str, data: Dict[str, Any], stream: bool = False) -> requests.Response:
        """
        发送API请求
        
        Args:
            endpoint: API端点
            data: 请求数据
            stream: 是否流式请求
            
        Returns:
            响应对象
            
        Raises:
            MiniMaxTTSError: API请求失败
        """
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = self.session.post(url, json=data, stream=stream, timeout=30)
            
            if not stream and response.status_code != 200:
                error_msg = f"API请求失败: {response.status_code}"
                try:
                    error_detail = response.json()
                    error_msg += f" - {error_detail.get('error', {}).get('message', '未知错误')}"
                except:
                    error_msg += f" - {response.text}"
                raise MiniMaxTTSError(error_msg)
                
            return response
            
        except requests.exceptions.RequestException as e:
            raise MiniMaxTTSError(f"网络请求失败: {str(e)}")
    
    def synthesize_sync(self, text: str, config: Optional[TTSConfig] = None) -> bytes:
        """
        同步语音合成
        
        Args:
            text: 要合成的文本
            config: TTS配置，如果为None则使用默认配置
            
        Returns:
            音频数据字节
            
        Raises:
            MiniMaxTTSError: 合成失败
        """
        if config is None:
            config = TTSConfig()
            
        data = {
            "model": "speech-01",
            "text": text,
            "voice_id": config.voice_id,
            "audio_format": config.audio_format,
            "bitrate": config.bitrate,
            "speed": config.speed,
            "vol": config.vol,
            "pitch": config.pitch
        }
        
        response = self._make_request(f"/v1/t2a_v2", data)
        
        # 检查响应内容类型
        content_type = response.headers.get('content-type', '')
        if 'application/json' in content_type:
            # 如果返回JSON，说明可能是错误响应
            try:
                error_data = response.json()
                raise MiniMaxTTSError(f"合成失败: {error_data}")
            except json.JSONDecodeError:
                pass
        
        return response.content
    
    def synthesize_stream(self, text: str, config: Optional[TTSConfig] = None) -> Iterator[bytes]:
        """
        流式语音合成
        
        Args:
            text: 要合成的文本
            config: TTS配置，如果为None则使用默认配置
            
        Yields:
            音频数据块
            
        Raises:
            MiniMaxTTSError: 合成失败
        """
        if config is None:
            config = TTSConfig()
            
        data = {
            "model": "speech-01-turbo",  # 流式使用turbo模型
            "text": text,
            "voice_id": config.voice_id,
            "audio_format": config.audio_format,
            "bitrate": config.bitrate,
            "speed": config.speed,
            "vol": config.vol,
            "pitch": config.pitch,
            "stream": True
        }
        
        response = self._make_request(f"/v1/t2a_v2", data, stream=True)
        
        try:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    yield chunk
        except Exception as e:
            raise MiniMaxTTSError(f"流式合成失败: {str(e)}")
        finally:
            response.close()
    
    def get_voice_list(self) -> Dict[str, Any]:
        """
        获取可用语音列表
        
        Returns:
            语音列表数据
        """
        response = self._make_request(f"/v1/voice_list", {})
        return response.json()
    
    def close(self):
        """关闭客户端会话"""
        self.session.close()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
